# Production Environment Configuration
# This file should be used for production deployments

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY

# Site Configuration - Production URLs
NEXT_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
NEXT_PUBLIC_ADMIN_URL=https://www.oceansoulsparkles.com.au/admin

# OneSignal configuration (can be the same as development)
NEXT_PUBLIC_ONESIGNAL_APP_ID=************************************
NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID=web.onesignal.auto.************************************
ONESIGNAL_REST_API_KEY=nivzmsejbeoiehtwsovd4sjyq

# Production Security Configuration
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_DEBUG_AUTH=false
ENABLE_AUTH_BYPASS=false

# Square Configuration - Update these for production
NEXT_PUBLIC_SQUARE_APPLICATION_ID=*****************************
NEXT_PUBLIC_SQUARE_LOCATION_ID=LBZPW61WHXG6F
SQUARE_ACCESS_TOKEN=EAAAlxZCtNDxeoYeFwwmBQxmQVrCWbjCW9G7eiI5GSvaaJGcSfm38Zkm_ENBIPNX
SQUARE_ENVIRONMENT=production  # Server-side only
NEXT_PUBLIC_SQUARE_ENVIRONMENT=production  # Client-side accessible

# Security Headers
NEXT_PUBLIC_ENABLE_SECURITY_HEADERS=true
NEXT_PUBLIC_DISABLE_CONSOLE_LOGS=true

# Google Cloud Email Configuration (Gmail SMTP)
GMAIL_SMTP_HOST=smtp.gmail.com
GMAIL_SMTP_PORT=587
GMAIL_SMTP_SECURE=false
GMAIL_SMTP_USER=<EMAIL>
GMAIL_SMTP_APP_PASSWORD=jjmfjcfqqrzgsogy
GMAIL_FROM_NAME=OceanSoulSparkles
GMAIL_FROM_EMAIL=<EMAIL>

# Gmail API Configuration (Advanced option)
GOOGLE_CLIENT_ID=429450887908-bdaro6svl47787ec21bi11251eoolj9n.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
ENABLE_AUTH_BYPASS=false  # For development only
# GOOGLE_REFRESH_TOKEN=your-refresh-token
# GOOGLE_ACCESS_TOKEN=your-access-token