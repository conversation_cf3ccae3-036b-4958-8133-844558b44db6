import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') ||
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    if (req.method === 'GET') {
      return handleGetBookings(req, res, user);
    } else if (req.method === 'POST') {
      return handleCreateBooking(req, res, user);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Bookings API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetBookings(req: NextApiRequest, res: NextApiResponse, user: any) {

    // Get bookings with customer and service details
    let query = supabase
      .from('bookings')
      .select(`
        id,
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        created_at,
        customer_id,
        service_id,
        assigned_artist_id,
        customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        services (
          id,
          name,
          duration,
          price
        ),
        artist_profiles!assigned_artist_id (
          id,
          artist_name,
          display_name
        )
      `)
      .order('booking_date', { ascending: false })
      .order('start_time', { ascending: false });

    // If user is an artist, only show their bookings
    if (user.role === 'Artist' || user.role === 'Braider') {
      query = query.eq('assigned_artist_id', user.id);
    }

    const { data: bookings, error } = await query;

    if (error) {
      console.error('Bookings query error:', error);
      return res.status(500).json({ error: 'Failed to fetch bookings' });
    }    // Transform data to match expected format
    const transformedBookings = (bookings || []).map(booking => {
      const customer = Array.isArray(booking.customers) ? booking.customers[0] : booking.customers;
      const service = Array.isArray(booking.services) ? booking.services[0] : booking.services;
      const artist = Array.isArray(booking.artist_profiles) ? booking.artist_profiles[0] : booking.artist_profiles;

      // Extract time from start_time timestamp
      const startTime = new Date(booking.start_time);
      const bookingTime = startTime.toTimeString().slice(0, 5); // HH:MM format

      return {
        id: booking.id,
        customer_name: customer ?
          `${customer.first_name} ${customer.last_name}` : 'Unknown Customer',
        customer_email: customer?.email || '',
        customer_phone: customer?.phone || '',
        service_name: service?.name || 'Unknown Service',
        service_duration: service?.duration || 0,
        service_price: service?.price || 0,
        artist_name: artist?.artist_name || artist?.display_name || 'Unassigned',
        booking_date: booking.booking_date,
        booking_time: bookingTime,
        start_time: booking.start_time,
        end_time: booking.end_time,
        status: booking.status,
        total_amount: booking.total_amount,
        notes: booking.notes,
        created_at: booking.created_at,
        // Additional fields for frontend compatibility
        customers: customer,
        services: service,
        artist_profiles: artist
      };
    });

    return res.status(200).json({
      bookings: transformedBookings,
      total: transformedBookings.length
    });
}
async function handleCreateBooking(req: NextApiRequest, res: NextApiResponse, user: any) {
  const {
    customer_id,
    service_id,
    assigned_artist_id,
    start_time,
    end_time,
    status = 'confirmed',
    total_amount,
    notes,
    location = 'Studio',
    tier_name,
    tier_price,
    booking_source = 'admin'
  } = req.body;

  // Validate required fields
  if (!customer_id || !service_id || !assigned_artist_id || !start_time || !end_time) {
    return res.status(400).json({
      error: 'Missing required fields',
      message: 'customer_id, service_id, assigned_artist_id, start_time, and end_time are required'
    });
  }

  try {
    // Validate that the customer exists
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('id, first_name, last_name')
      .eq('id', customer_id)
      .single();

    if (customerError || !customer) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    // Validate that the service exists
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('id, name, base_price')
      .eq('id', service_id)
      .single();

    if (serviceError || !service) {
      return res.status(400).json({ error: 'Invalid service ID' });
    }

    // Validate that the artist exists
    const { data: artist, error: artistError } = await supabase
      .from('artist_profiles')
      .select('id, artist_name')
      .eq('id', assigned_artist_id)
      .single();

    if (artistError || !artist) {
      return res.status(400).json({ error: 'Invalid artist ID' });
    }

    // Check for booking conflicts
    const { data: conflictingBookings, error: conflictError } = await supabase
      .from('bookings')
      .select('id, start_time, end_time')
      .eq('assigned_artist_id', assigned_artist_id)
      .neq('status', 'cancelled')
      .or(`and(start_time.lte.${start_time},end_time.gt.${start_time}),and(start_time.lt.${end_time},end_time.gte.${end_time}),and(start_time.gte.${start_time},end_time.lte.${end_time})`);

    if (conflictError) {
      console.error('Conflict check error:', conflictError);
    } else if (conflictingBookings && conflictingBookings.length > 0) {
      return res.status(400).json({
        error: 'Booking conflict',
        message: 'The selected artist already has a booking during this time slot'
      });
    }

    // Create the booking
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .insert([{
        customer_id,
        service_id,
        artist_id: assigned_artist_id,
        assigned_artist_id,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        location,
        tier_name,
        tier_price,
        booking_source
      }])
      .select(`
        id,
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        location,
        tier_name,
        tier_price,
        booking_source,
        created_at
      `)
      .single();

    if (bookingError) {
      console.error('Booking creation error:', bookingError);
      return res.status(500).json({ error: 'Failed to create booking' });
    }

    return res.status(201).json({
      message: 'Booking created successfully',
      booking: {
        ...booking,
        customer: customer,
        service: service,
        artist: artist
      }
    });

  } catch (error) {
    console.error('Create booking error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
